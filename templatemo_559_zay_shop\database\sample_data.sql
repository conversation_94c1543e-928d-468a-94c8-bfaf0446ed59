-- Sample Data for Zay Shop E-commerce Database
-- Insert test data for development and testing

USE zay_shop_db;

-- Insert admin user (password: admin123)
INSERT INTO users (username, email, password_hash, first_name, last_name, phone, address, city, state, zip_code, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', '555-0001', '123 Admin St', 'New York', 'NY', '10001', 'admin', 'active', TRUE),
('john_doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<PERSON>', 'Doe', '555-0002', '456 Customer Ave', 'Los Angeles', 'CA', '90001', 'customer', 'active', TRUE),
('jane_smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Smith', '555-0003', '789 Buyer Blvd', 'Chicago', 'IL', '60601', 'customer', 'active', TRUE);

-- Insert categories
INSERT INTO categories (name, slug, description, image, sort_order, status) VALUES
('Watches', 'watches', 'Premium timepieces for every occasion', 'category_img_01.jpg', 1, 'active'),
('Shoes', 'shoes', 'Comfortable and stylish footwear', 'category_img_02.jpg', 2, 'active'),
('Accessories', 'accessories', 'Fashion accessories and more', 'category_img_03.jpg', 3, 'active'),
('Men\'s Fashion', 'mens-fashion', 'Clothing and accessories for men', NULL, 4, 'active'),
('Women\'s Fashion', 'womens-fashion', 'Clothing and accessories for women', NULL, 5, 'active'),
('Sports & Fitness', 'sports-fitness', 'Equipment for active lifestyle', NULL, 6, 'active');

-- Insert products
INSERT INTO products (name, slug, description, short_description, sku, price, sale_price, stock_quantity, category_id, brand, tags, featured, status) VALUES
('Gym Weight Set', 'gym-weight-set', 'Professional gym weight set perfect for home workouts. Includes various weight plates and adjustable dumbbells for comprehensive strength training.', 'Professional gym weight set for home workouts', 'GYM-WEIGHT-001', 240.00, NULL, 15, 6, 'FitPro', 'gym, weights, fitness, strength training', TRUE, 'active'),
('Cloud Nike Shoes', 'cloud-nike-shoes', 'Ultra-comfortable Nike shoes with cloud-like cushioning technology. Perfect for running, walking, and everyday wear with superior comfort and style.', 'Ultra-comfortable Nike shoes with cloud cushioning', 'NIKE-CLOUD-001', 480.00, 420.00, 25, 2, 'Nike', 'shoes, nike, running, comfort, sports', TRUE, 'active'),
('Summer Adidas Shoes', 'summer-adidas-shoes', 'Lightweight and breathable Adidas shoes designed for summer activities. Features moisture-wicking technology and superior ventilation for all-day comfort.', 'Lightweight Adidas shoes perfect for summer', 'ADIDAS-SUM-001', 360.00, NULL, 30, 2, 'Adidas', 'shoes, adidas, summer, lightweight, breathable', TRUE, 'active'),
('Classic Leather Watch', 'classic-leather-watch', 'Elegant leather strap watch with classic design. Features precision quartz movement and water resistance up to 50 meters.', 'Elegant leather strap watch with classic design', 'WATCH-LEA-001', 299.00, 249.00, 20, 1, 'TimeClassic', 'watch, leather, classic, elegant, accessories', FALSE, 'active'),
('Sport Digital Watch', 'sport-digital-watch', 'Advanced digital sports watch with multiple functions including stopwatch, alarm, and water resistance. Perfect for athletes and fitness enthusiasts.', 'Advanced digital sports watch with multiple functions', 'WATCH-DIG-001', 189.00, NULL, 35, 1, 'SportTech', 'watch, digital, sports, fitness, waterproof', FALSE, 'active'),
('Designer Sunglasses', 'designer-sunglasses', 'Premium designer sunglasses with UV protection and polarized lenses. Stylish frame design suitable for any occasion.', 'Premium designer sunglasses with UV protection', 'SUNGLASS-001', 159.00, 129.00, 40, 3, 'StyleVision', 'sunglasses, designer, UV protection, fashion', FALSE, 'active'),
('Leather Handbag', 'leather-handbag', 'Genuine leather handbag with spacious interior and multiple compartments. Perfect for work, travel, or everyday use.', 'Genuine leather handbag with spacious interior', 'HANDBAG-001', 199.00, NULL, 18, 3, 'LuxeBags', 'handbag, leather, fashion, accessories, women', FALSE, 'active'),
('Casual T-Shirt', 'casual-t-shirt', 'Comfortable cotton t-shirt perfect for casual wear. Available in multiple colors and sizes with soft, breathable fabric.', 'Comfortable cotton t-shirt for casual wear', 'TSHIRT-CAS-001', 29.99, 24.99, 100, 4, 'ComfortWear', 't-shirt, casual, cotton, comfortable, men', FALSE, 'active'),
('Running Shorts', 'running-shorts', 'High-performance running shorts with moisture-wicking fabric and built-in compression shorts. Perfect for running and gym workouts.', 'High-performance running shorts with moisture-wicking', 'SHORTS-RUN-001', 49.99, NULL, 75, 6, 'ActiveGear', 'shorts, running, sports, moisture-wicking, fitness', FALSE, 'active'),
('Yoga Mat Premium', 'yoga-mat-premium', 'Premium non-slip yoga mat with extra thickness for comfort. Made from eco-friendly materials with excellent grip and durability.', 'Premium non-slip yoga mat with extra thickness', 'YOGA-MAT-001', 79.99, 69.99, 50, 6, 'ZenFit', 'yoga, mat, fitness, non-slip, eco-friendly', FALSE, 'active');

-- Insert product images
INSERT INTO product_images (product_id, image_path, alt_text, sort_order, is_primary) VALUES
(1, 'feature_prod_01.jpg', 'Gym Weight Set - Main Image', 1, TRUE),
(2, 'feature_prod_02.jpg', 'Cloud Nike Shoes - Main Image', 1, TRUE),
(3, 'feature_prod_03.jpg', 'Summer Adidas Shoes - Main Image', 1, TRUE),
(4, 'shop_01.jpg', 'Classic Leather Watch - Main Image', 1, TRUE),
(5, 'shop_02.jpg', 'Sport Digital Watch - Main Image', 1, TRUE),
(6, 'shop_03.jpg', 'Designer Sunglasses - Main Image', 1, TRUE),
(7, 'shop_04.jpg', 'Leather Handbag - Main Image', 1, TRUE),
(8, 'shop_05.jpg', 'Casual T-Shirt - Main Image', 1, TRUE),
(9, 'shop_06.jpg', 'Running Shorts - Main Image', 1, TRUE),
(10, 'shop_07.jpg', 'Yoga Mat Premium - Main Image', 1, TRUE);

-- Insert product attributes
INSERT INTO product_attributes (product_id, attribute_name, attribute_value, stock_quantity) VALUES
(2, 'Size', '8', 5),
(2, 'Size', '9', 8),
(2, 'Size', '10', 7),
(2, 'Size', '11', 5),
(2, 'Color', 'White', 12),
(2, 'Color', 'Black', 8),
(2, 'Color', 'Blue', 5),
(3, 'Size', '8', 6),
(3, 'Size', '9', 10),
(3, 'Size', '10', 8),
(3, 'Size', '11', 6),
(3, 'Color', 'White', 15),
(3, 'Color', 'Red', 10),
(3, 'Color', 'Green', 5),
(8, 'Size', 'S', 25),
(8, 'Size', 'M', 30),
(8, 'Size', 'L', 25),
(8, 'Size', 'XL', 20),
(8, 'Color', 'White', 30),
(8, 'Color', 'Black', 25),
(8, 'Color', 'Blue', 20),
(8, 'Color', 'Red', 25);

-- Insert sample orders
INSERT INTO orders (order_number, user_id, status, total_amount, subtotal, tax_amount, shipping_amount, payment_method, payment_status, shipping_address, billing_address) VALUES
('ORD-2024-001', 2, 'delivered', 519.99, 480.00, 39.99, 0.00, 'credit_card', 'paid', '456 Customer Ave, Los Angeles, CA 90001', '456 Customer Ave, Los Angeles, CA 90001'),
('ORD-2024-002', 3, 'processing', 389.98, 359.99, 29.99, 0.00, 'paypal', 'paid', '789 Buyer Blvd, Chicago, IL 60601', '789 Buyer Blvd, Chicago, IL 60601'),
('ORD-2024-003', 2, 'pending', 269.98, 249.99, 19.99, 0.00, 'credit_card', 'pending', '456 Customer Ave, Los Angeles, CA 90001', '456 Customer Ave, Los Angeles, CA 90001');

-- Insert order items
INSERT INTO order_items (order_id, product_id, product_name, product_sku, quantity, unit_price, total_price, product_attributes) VALUES
(1, 2, 'Cloud Nike Shoes', 'NIKE-CLOUD-001', 1, 480.00, 480.00, '{"size": "9", "color": "White"}'),
(2, 3, 'Summer Adidas Shoes', 'ADIDAS-SUM-001', 1, 360.00, 360.00, '{"size": "10", "color": "White"}'),
(3, 4, 'Classic Leather Watch', 'WATCH-LEA-001', 1, 249.00, 249.00, NULL);

-- Insert website settings
INSERT INTO website_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'Zay Shop', 'text', 'Website name'),
('site_description', 'Premium eCommerce store for fashion and lifestyle', 'text', 'Website description'),
('contact_email', '<EMAIL>', 'text', 'Contact email address'),
('contact_phone', '************', 'text', 'Contact phone number'),
('currency', 'USD', 'text', 'Default currency'),
('tax_rate', '8.25', 'number', 'Tax rate percentage'),
('shipping_cost', '0.00', 'number', 'Default shipping cost'),
('free_shipping_threshold', '100.00', 'number', 'Minimum order for free shipping'),
('items_per_page', '12', 'number', 'Products per page in shop'),
('enable_reviews', 'true', 'boolean', 'Enable product reviews'),
('enable_coupons', 'true', 'boolean', 'Enable coupon system'),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode');

-- Insert sample coupons
INSERT INTO coupons (code, type, value, minimum_amount, usage_limit, valid_until, status) VALUES
('WELCOME10', 'percentage', 10.00, 50.00, 100, DATE_ADD(NOW(), INTERVAL 30 DAY), 'active'),
('SAVE20', 'fixed', 20.00, 100.00, 50, DATE_ADD(NOW(), INTERVAL 60 DAY), 'active'),
('SUMMER15', 'percentage', 15.00, 75.00, NULL, DATE_ADD(NOW(), INTERVAL 90 DAY), 'active');

-- Insert sample reviews
INSERT INTO product_reviews (product_id, user_id, rating, title, review_text, status) VALUES
(1, 2, 5, 'Excellent quality!', 'Great gym weight set, very sturdy and well-made. Perfect for home workouts.', 'approved'),
(2, 3, 4, 'Very comfortable', 'These Nike shoes are incredibly comfortable. Great for running and daily wear.', 'approved'),
(3, 2, 5, 'Love these shoes!', 'Perfect summer shoes, very lightweight and breathable. Highly recommend!', 'approved'),
(4, 3, 4, 'Classic and elegant', 'Beautiful watch with great craftsmanship. The leather strap is very comfortable.', 'approved');
