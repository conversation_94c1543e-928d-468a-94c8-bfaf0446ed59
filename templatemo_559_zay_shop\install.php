<?php
/**
 * Installation Script for Zay Shop E-commerce System
 * 
 * This script helps set up the database and initial configuration
 * for the Zay Shop e-commerce management system.
 */

// Prevent running if already installed
if (file_exists('config/installed.lock')) {
    die('System is already installed. Delete config/installed.lock to reinstall.');
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            // Database configuration
            $result = testDatabaseConnection($_POST);
            if ($result['success']) {
                saveDatabaseConfig($_POST);
                $success = 'Database connection successful!';
                $step = 3;
            } else {
                $error = $result['message'];
            }
            break;
            
        case 3:
            // Database setup
            $result = setupDatabase();
            if ($result['success']) {
                $success = 'Database setup completed!';
                $step = 4;
            } else {
                $error = $result['message'];
            }
            break;
            
        case 4:
            // Admin account setup
            $result = createAdminAccount($_POST);
            if ($result['success']) {
                $success = 'Admin account created successfully!';
                $step = 5;
            } else {
                $error = $result['message'];
            }
            break;
            
        case 5:
            // Finalize installation
            finalizeInstallation();
            $step = 6;
            break;
    }
}

/**
 * Test database connection
 */
function testDatabaseConnection($config) {
    try {
        $dsn = "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['db_user'], $config['db_pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return ['success' => true];
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()];
    }
}

/**
 * Save database configuration
 */
function saveDatabaseConfig($config) {
    $configContent = "<?php\n";
    $configContent .= "define('DB_HOST', '{$config['db_host']}');\n";
    $configContent .= "define('DB_NAME', '{$config['db_name']}');\n";
    $configContent .= "define('DB_USER', '{$config['db_user']}');\n";
    $configContent .= "define('DB_PASS', '{$config['db_pass']}');\n";
    $configContent .= "define('DB_CHARSET', 'utf8mb4');\n";
    
    file_put_contents('config/database_config.php', $configContent);
}

/**
 * Setup database tables
 */
function setupDatabase() {
    try {
        require_once 'config/database_config.php';
        
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Read and execute schema
        $schema = file_get_contents('database/schema.sql');
        $statements = explode(';', $schema);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // Import sample data if requested
        if (file_exists('database/sample_data.sql')) {
            $sampleData = file_get_contents('database/sample_data.sql');
            $statements = explode(';', $sampleData);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
        }
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Database setup failed: ' . $e->getMessage()];
    }
}

/**
 * Create admin account
 */
function createAdminAccount($data) {
    try {
        require_once 'config/database_config.php';
        
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Hash password
        $hashedPassword = password_hash($data['admin_password'], PASSWORD_DEFAULT);
        
        // Update admin user
        $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password_hash = ?, first_name = ?, last_name = ? WHERE role = 'admin' LIMIT 1");
        $stmt->execute([
            $data['admin_username'],
            $data['admin_email'],
            $hashedPassword,
            $data['admin_first_name'],
            $data['admin_last_name']
        ]);
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Admin account creation failed: ' . $e->getMessage()];
    }
}

/**
 * Finalize installation
 */
function finalizeInstallation() {
    // Create installation lock file
    file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
    
    // Create upload directories
    if (!is_dir('assets/img/products')) {
        mkdir('assets/img/products', 0755, true);
    }
    
    // Set proper permissions
    chmod('assets/img/products', 0755);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <title>Install Zay Shop E-commerce System</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/templatemo.css">
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    
    <style>
        body { background-color: #f8f9fc; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .step-indicator { margin-bottom: 30px; }
        .step { display: inline-block; width: 40px; height: 40px; line-height: 40px; text-align: center; border-radius: 50%; margin-right: 10px; }
        .step.active { background-color: #59ab6e; color: white; }
        .step.completed { background-color: #28a745; color: white; }
        .step.pending { background-color: #e9ecef; color: #6c757d; }
    </style>
</head>

<body>
    <div class="container">
        <div class="install-container">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h2 class="mb-0">
                        <i class="fas fa-store me-2"></i>
                        Zay Shop Installation
                    </h2>
                </div>
                
                <div class="card-body">
                    <!-- Step Indicator -->
                    <div class="step-indicator text-center">
                        <span class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</span>
                        <span class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</span>
                        <span class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'pending'; ?>">3</span>
                        <span class="step <?php echo $step >= 4 ? ($step > 4 ? 'completed' : 'active') : 'pending'; ?>">4</span>
                        <span class="step <?php echo $step >= 5 ? ($step > 5 ? 'completed' : 'active') : 'pending'; ?>">5</span>
                        <span class="step <?php echo $step >= 6 ? 'completed' : 'pending'; ?>">6</span>
                    </div>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step == 1): ?>
                        <!-- Welcome -->
                        <h4>Welcome to Zay Shop Installation</h4>
                        <p>This installer will help you set up your e-commerce website. Please ensure you have:</p>
                        <ul>
                            <li>PHP 7.4 or higher</li>
                            <li>MySQL 5.7 or higher</li>
                            <li>Write permissions on the installation directory</li>
                            <li>Database credentials ready</li>
                        </ul>
                        <div class="text-end">
                            <a href="?step=2" class="btn btn-success">
                                Start Installation <i class="fas fa-arrow-right ms-2"></i>
                            </a>
                        </div>
                        
                    <?php elseif ($step == 2): ?>
                        <!-- Database Configuration -->
                        <h4>Database Configuration</h4>
                        <p>Please enter your database connection details:</p>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="db_host" class="form-label">Database Host</label>
                                <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_name" class="form-label">Database Name</label>
                                <input type="text" class="form-control" id="db_name" name="db_name" value="zay_shop_db" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_user" class="form-label">Database Username</label>
                                <input type="text" class="form-control" id="db_user" name="db_user" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_pass" class="form-label">Database Password</label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass">
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-success">
                                    Test Connection <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </form>
                        
                    <?php elseif ($step == 3): ?>
                        <!-- Database Setup -->
                        <h4>Database Setup</h4>
                        <p>Click the button below to create the database tables and import sample data:</p>
                        
                        <form method="POST">
                            <div class="text-end">
                                <button type="submit" class="btn btn-success">
                                    Setup Database <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </form>
                        
                    <?php elseif ($step == 4): ?>
                        <!-- Admin Account -->
                        <h4>Create Admin Account</h4>
                        <p>Create your administrator account:</p>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="admin_first_name" class="form-label">First Name</label>
                                    <input type="text" class="form-control" id="admin_first_name" name="admin_first_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="admin_last_name" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" id="admin_last_name" name="admin_last_name" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="admin_username" name="admin_username" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-success">
                                    Create Account <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </form>
                        
                    <?php elseif ($step == 5): ?>
                        <!-- Finalize -->
                        <h4>Finalize Installation</h4>
                        <p>Complete the installation process:</p>
                        
                        <form method="POST">
                            <div class="text-end">
                                <button type="submit" class="btn btn-success">
                                    Complete Installation <i class="fas fa-check ms-2"></i>
                                </button>
                            </div>
                        </form>
                        
                    <?php elseif ($step == 6): ?>
                        <!-- Complete -->
                        <div class="text-center">
                            <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                            <h4>Installation Complete!</h4>
                            <p>Your Zay Shop e-commerce system has been successfully installed.</p>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <a href="index.php" class="btn btn-primary w-100">
                                        <i class="fas fa-home me-2"></i>
                                        Visit Website
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="admin/login.php" class="btn btn-success w-100">
                                        <i class="fas fa-shield-alt me-2"></i>
                                        Admin Panel
                                    </a>
                                </div>
                            </div>
                            
                            <div class="alert alert-warning mt-4">
                                <strong>Important:</strong> For security reasons, please delete the <code>install.php</code> file from your server.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>
