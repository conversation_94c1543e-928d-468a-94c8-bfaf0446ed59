# 📤 دليل رفع المتجر للاستضافة المجانية

## 🎯 خطوات الرفع التفصيلية:

### الخطوة 1: تحضير الملفات
```bash
1. اضغ<PERSON> مجلد templatemo_559_zay_shop كاملاً
2. اسم الملف المضغوط: zay-shop.zip
3. تأكد من أن الملف أقل من 50MB
```

### الخطوة 2: رفع عبر File Manager
```
1. سجل دخول إلى cPanel
2. اضغط "File Manager"
3. انتقل إلى مجلد "htdocs" أو "public_html"
4. اضغط "Upload"
5. اختر ملف zay-shop.zip
6. انتظر انتهاء الرفع
7. اضغط بالزر الأيمن على الملف > "Extract"
8. احذف الملف المضغوط بعد فك الضغط
```

### الخطوة 3: ترتيب الملفات
```
يجب أن تكون الملفات مرتبة هكذا:
htdocs/
├── admin/
├── assets/
├── config/
├── database/
├── includes/
├── index.html
├── login.php
├── install.php
└── README.md
```

## 🗄️ إعداد قاعدة البيانات:

### في cPanel:
```
1. اضغط "MySQL Databases"
2. أنشئ قاعدة بيانات:
   - اسم قاعدة البيانات: zay_shop
3. أنشئ مستخدم:
   - اسم المستخدم: zay_user
   - كلمة المرور: [كلمة مرور قوية]
4. اربط المستخدم بقاعدة البيانات
5. امنح "جميع الصلاحيات"
```

### معلومات الاتصال:
```
Database Host: localhost
Database Name: [username]_zay_shop
Database User: [username]_zay_user
Database Password: [كلمة المرور التي اخترتها]
```

## ⚙️ تشغيل التثبيت:

### الخطوة 1: فحص المتطلبات
```
انتقل إلى: https://yoursite.epizy.com/check-requirements.php
تأكد من ظهور جميع العلامات الخضراء
```

### الخطوة 2: بدء التثبيت
```
انتقل إلى: https://yoursite.epizy.com/install.php
اتبع الخطوات:
1. مرحباً بك
2. إعداد قاعدة البيانات
3. إنشاء الجداول
4. إنشاء حساب المدير
5. إنهاء التثبيت
```

### الخطوة 3: تسجيل الدخول
```
رابط الإدارة: https://yoursite.epizy.com/admin/login.php
المدير الافتراضي:
- اسم المستخدم: admin
- كلمة المرور: admin123
⚠️ غير كلمة المرور فوراً!
```

## 🔧 إعدادات مهمة بعد التثبيت:

### 1. الأمان:
```bash
# احذف هذه الملفات:
- install.php
- check-requirements.php
```

### 2. إعداد الصلاحيات:
```bash
# في File Manager:
assets/img/products/ -> 755
config/ -> 755
```

### 3. إعداد .htaccess:
```apache
# أنشئ ملف .htaccess في المجلد الرئيسي:
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# حماية ملفات الإعدادات
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>
```

## 🚨 حل المشاكل الشائعة:

### مشكلة: خطأ 500
```
الحل:
1. تحقق من صلاحيات الملفات
2. راجع ملف .htaccess
3. تحقق من سجل الأخطاء في cPanel
```

### مشكلة: خطأ قاعدة البيانات
```
الحل:
1. تأكد من صحة بيانات الاتصال
2. تحقق من وجود قاعدة البيانات
3. تأكد من صلاحيات المستخدم
```

### مشكلة: الصور لا تظهر
```
الحل:
1. تحقق من صلاحيات مجلد assets/img/
2. تأكد من رفع جميع ملفات الصور
3. تحقق من مسارات الصور في الكود
```

## 📊 اختبار الموقع:

### قائمة التحقق:
```
✅ الصفحة الرئيسية تعمل
✅ تسجيل دخول الإدارة يعمل
✅ إضافة منتج جديد
✅ تسجيل عميل جديد
✅ البحث في المنتجات
✅ سلة التسوق تعمل
```

## 🎯 نصائح للاستضافة المجانية:

### المميزات:
```
✅ مجانية تماماً
✅ دعم PHP و MySQL
✅ SSL مجاني
✅ cPanel سهل الاستخدام
```

### القيود:
```
⚠️ مساحة محدودة
⚠️ عرض نقل محدود
⚠️ قد تكون أبطأ من المدفوعة
⚠️ دعم فني محدود
```

### نصائح للأداء:
```
1. ضغط الصور قبل الرفع
2. استخدم ملفات CSS و JS مضغوطة
3. فعل التخزين المؤقت
4. تجنب الملفات الكبيرة
```

## 🔄 الترقية للمدفوعة:

### متى تحتاج الترقية:
```
- عندما تزيد الزيارات عن 1000/يوم
- عندما تحتاج مساحة أكبر
- عندما تريد نطاق مخصص
- عندما تحتاج دعم فني أفضل
```

### خيارات الترقية:
```
🥇 Hostinger: 3$/شهر
🥈 SiteGround: 4$/شهر  
🥉 Bluehost: 5$/شهر
```

---

## 🎉 تهانينا!

**متجرك الإلكتروني الآن مباشر ومجاني!**

الروابط المهمة:
- الموقع: https://yoursite.epizy.com
- الإدارة: https://yoursite.epizy.com/admin/login.php
- تسجيل العملاء: https://yoursite.epizy.com/login.php

**ابدأ الآن في إضافة منتجاتك والبيع!** 🛍️✨
