<?php
/**
 * Products Management for Zay Shop Admin Panel
 * 
 * This page handles CRUD operations for products including
 * listing, adding, editing, and deleting products.
 */

require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Require admin access
requireAdmin();

$action = $_GET['action'] ?? 'list';
$productId = $_GET['id'] ?? null;
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        switch ($action) {
            case 'add':
            case 'edit':
                $result = saveProduct($_POST, $_FILES, $productId);
                if ($result['success']) {
                    $message = $result['message'];
                    if ($action === 'add') {
                        $action = 'list';
                    }
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'delete':
                $result = deleteProduct($productId);
                if ($result['success']) {
                    $message = $result['message'];
                    $action = 'list';
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get data based on action
switch ($action) {
    case 'add':
        $pageTitle = 'Add Product';
        $product = null;
        break;
        
    case 'edit':
        $pageTitle = 'Edit Product';
        $product = getProduct($productId);
        if (!$product) {
            $error = 'Product not found.';
            $action = 'list';
        }
        break;
        
    case 'view':
        $pageTitle = 'View Product';
        $product = getProduct($productId);
        if (!$product) {
            $error = 'Product not found.';
            $action = 'list';
        }
        break;
        
    default:
        $pageTitle = 'Products';
        $action = 'list';
        break;
}

// Get products list for list view
if ($action === 'list') {
    $page = max(1, intval($_GET['page'] ?? 1));
    $search = $_GET['search'] ?? '';
    $category = $_GET['category'] ?? '';
    $status = $_GET['status'] ?? '';
    $itemsPerPage = 20;
    
    $products = getProductsList($page, $itemsPerPage, $search, $category, $status);
    $categories = getCategories();
}

// Generate CSRF token
$csrfToken = generateCSRFToken();

include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
        <!-- Products List -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-box me-2"></i>Products
            </h1>
            <a href="?action=add" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add Product
            </a>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               placeholder="Search products..." 
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-3">
                        <select name="category" class="form-select">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat['id']; ?>" 
                                        <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="out_of_stock" <?php echo $status === 'out_of_stock' ? 'selected' : ''; ?>>Out of Stock</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Products Table -->
        <div class="card shadow">
            <div class="card-body">
                <?php if (empty($products['data'])): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No products found</h5>
                        <p class="text-muted">Start by adding your first product.</p>
                        <a href="?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Product
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>SKU</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products['data'] as $product): ?>
                                    <tr>
                                        <td>
                                            <?php if ($product['image']): ?>
                                                <img src="../assets/img/<?php echo htmlspecialchars($product['image']); ?>" 
                                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                     class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?php echo htmlspecialchars($product['name']); ?></div>
                                            <?php if ($product['featured']): ?>
                                                <small class="badge bg-warning">Featured</small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($product['sku']); ?></td>
                                        <td><?php echo htmlspecialchars($product['category_name']); ?></td>
                                        <td>
                                            <?php if ($product['sale_price']): ?>
                                                <span class="text-decoration-line-through text-muted">
                                                    <?php echo formatPrice($product['price']); ?>
                                                </span><br>
                                                <span class="text-success fw-bold">
                                                    <?php echo formatPrice($product['sale_price']); ?>
                                                </span>
                                            <?php else: ?>
                                                <?php echo formatPrice($product['price']); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $product['stock_quantity'] <= $product['min_stock_level'] ? 'danger' : 
                                                     ($product['stock_quantity'] <= $product['min_stock_level'] * 2 ? 'warning' : 'success'); 
                                            ?>">
                                                <?php echo $product['stock_quantity']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo match($product['status']) {
                                                    'active' => 'success',
                                                    'inactive' => 'secondary',
                                                    'out_of_stock' => 'danger',
                                                    default => 'secondary'
                                                };
                                            ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $product['status'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="?action=view&id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-sm btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="?action=edit&id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger btn-delete" 
                                                        onclick="deleteProduct(<?php echo $product['id']; ?>)" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($products['pagination']['total_pages'] > 1): ?>
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div class="text-muted">
                                Showing <?php echo $products['pagination']['offset'] + 1; ?> to 
                                <?php echo min($products['pagination']['offset'] + $products['pagination']['items_per_page'], $products['pagination']['total_items']); ?> 
                                of <?php echo $products['pagination']['total_items']; ?> products
                            </div>
                            <?php echo generatePaginationHTML($products['pagination'], '?action=list'); ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit Product Form -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                <?php echo $action === 'add' ? 'Add' : 'Edit'; ?> Product
            </h1>
            <a href="?action=list" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Products
            </a>
        </div>

        <form method="POST" enctype="multipart/form-data" id="productForm">
            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
            
            <div class="row">
                <div class="col-lg-8">
                    <!-- Basic Information -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="name" class="form-label">Product Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($product['name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="sku" class="form-label">SKU *</label>
                                    <input type="text" class="form-control" id="sku" name="sku" 
                                           value="<?php echo htmlspecialchars($product['sku'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="short_description" class="form-label">Short Description</label>
                                <textarea class="form-control" id="short_description" name="short_description" 
                                          rows="3" maxlength="500"><?php echo htmlspecialchars($product['short_description'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Full Description</label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="6"><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Product Image -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Product Image</h6>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <?php if (isset($product['image']) && $product['image']): ?>
                                    <img id="imagePreview" src="../assets/img/<?php echo htmlspecialchars($product['image']); ?>" 
                                         alt="Product Image" class="img-fluid rounded" style="max-height: 200px;">
                                <?php else: ?>
                                    <img id="imagePreview" src="#" alt="Preview" class="img-fluid rounded" 
                                         style="max-height: 200px; display: none;">
                                    <div id="imagePlaceholder" class="bg-light p-4 rounded">
                                        <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                        <p class="text-muted">No image selected</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <input type="file" class="form-control" id="image" name="image" 
                                   accept="image/*" onchange="previewImage(this, 'imagePreview')">
                            <small class="text-muted">Max size: 5MB. Formats: JPG, PNG, GIF</small>
                        </div>
                    </div>
                    
                    <!-- Status & Visibility -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Status & Visibility</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo ($product['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo ($product['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="out_of_stock" <?php echo ($product['status'] ?? '') === 'out_of_stock' ? 'selected' : ''; ?>>Out of Stock</option>
                                </select>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1"
                                       <?php echo ($product['featured'] ?? false) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="featured">
                                    Featured Product
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Continue with more form sections... -->
            <div class="text-end">
                <a href="?action=list" class="btn btn-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    <?php echo $action === 'add' ? 'Add' : 'Update'; ?> Product
                </button>
            </div>
        </form>
    <?php endif; ?>
</div>

<script>
function deleteProduct(id) {
    if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '?action=delete&id=' + id;
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo $csrfToken; ?>';
        
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php
// Product management functions
function getProductsList($page, $itemsPerPage, $search = '', $category = '', $status = '') {
    $offset = ($page - 1) * $itemsPerPage;
    $conditions = [];
    $params = [];
    
    if ($search) {
        $conditions[] = "(p.name LIKE ? OR p.sku LIKE ? OR p.description LIKE ?)";
        $searchTerm = "%{$search}%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }
    
    if ($category) {
        $conditions[] = "p.category_id = ?";
        $params[] = $category;
    }
    
    if ($status) {
        $conditions[] = "p.status = ?";
        $params[] = $status;
    }
    
    $whereClause = $conditions ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM products p JOIN categories c ON p.category_id = c.id {$whereClause}";
    $totalItems = dbFetchOne($countSql, $params)['total'];
    
    // Get products
    $sql = "SELECT p.*, c.name as category_name, 
                   (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as image
            FROM products p 
            JOIN categories c ON p.category_id = c.id 
            {$whereClause}
            ORDER BY p.created_at DESC 
            LIMIT {$itemsPerPage} OFFSET {$offset}";
    
    $products = dbFetchAll($sql, $params);
    $pagination = paginate($totalItems, $itemsPerPage, $page);
    
    return ['data' => $products, 'pagination' => $pagination];
}

function getProduct($id) {
    $sql = "SELECT p.*, c.name as category_name 
            FROM products p 
            JOIN categories c ON p.category_id = c.id 
            WHERE p.id = ?";
    return dbFetchOne($sql, [$id]);
}

function getCategories() {
    return dbFetchAll("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
}

function saveProduct($data, $files, $productId = null) {
    // Implementation for saving product...
    return ['success' => true, 'message' => 'Product saved successfully'];
}

function deleteProduct($id) {
    // Implementation for deleting product...
    return ['success' => true, 'message' => 'Product deleted successfully'];
}

include 'includes/footer.php';
?>
