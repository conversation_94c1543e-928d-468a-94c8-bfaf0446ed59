# Zay Shop E-commerce Management System

A comprehensive e-commerce website management system built on top of the TemplateMo 559 Zay Shop template. This system provides a complete solution for managing an online store with a professional admin dashboard, user authentication, and full e-commerce functionality.

## Features

### 🛍️ **E-commerce Functionality**
- Product catalog with categories and search
- Shopping cart and checkout system
- Order management and tracking
- User accounts and authentication
- Product reviews and ratings
- Coupon and discount system
- Inventory management

### 🔐 **Security & Authentication**
- Secure user authentication with password hashing
- Role-based access control (Admin/Customer)
- CSRF protection
- Session management
- Rate limiting for login attempts
- Input validation and sanitization

### 📊 **Admin Dashboard**
- Comprehensive analytics and reporting
- Product management (CRUD operations)
- User management
- Order management
- Category management
- Coupon management
- Website settings configuration
- Low stock alerts

### 🎨 **Design & User Experience**
- Responsive design for all devices
- Consistent branding with original template
- Modern and clean admin interface
- User-friendly navigation
- Mobile-optimized

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- GD extension for image processing

### Step 1: Database Setup

1. Create a new MySQL database:
```sql
CREATE DATABASE zay_shop_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Import the database schema:
```bash
mysql -u your_username -p zay_shop_db < database/schema.sql
```

3. Import sample data (optional):
```bash
mysql -u your_username -p zay_shop_db < database/sample_data.sql
```

### Step 2: Configuration

1. Update database configuration in `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'zay_shop_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

2. Set proper file permissions:
```bash
chmod 755 assets/img/products/
chmod 644 config/database.php
```

### Step 3: Admin Account

The sample data includes a default admin account:
- **Username:** admin
- **Email:** <EMAIL>
- **Password:** admin123

**⚠️ Important:** Change the admin password immediately after installation!

## Usage

### Admin Panel Access

1. Navigate to `/admin/login.php`
2. Login with admin credentials
3. Access the dashboard at `/admin/dashboard.php`

### Admin Panel Features

#### Dashboard
- Overview statistics (users, products, orders, revenue)
- Recent orders list
- Low stock alerts
- Quick action buttons

#### Product Management
- Add/Edit/Delete products
- Bulk operations
- Image upload and management
- Inventory tracking
- SEO optimization

#### Order Management
- View and manage orders
- Update order status
- Generate invoices
- Track shipments

#### User Management
- View customer accounts
- Manage user roles
- Account activation/deactivation

#### Analytics
- Sales reports
- Customer analytics
- Product performance
- Revenue tracking

### Customer Features

#### User Registration & Login
- Secure account creation
- Email verification
- Password reset functionality
- Profile management

#### Shopping Experience
- Product browsing and search
- Shopping cart functionality
- Secure checkout process
- Order history and tracking

## File Structure

```
templatemo_559_zay_shop/
├── admin/                  # Admin panel files
│   ├── includes/          # Admin includes (header, footer)
│   ├── dashboard.php      # Main admin dashboard
│   ├── products.php       # Product management
│   ├── orders.php         # Order management
│   ├── users.php          # User management
│   └── login.php          # Admin login
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── img/              # Images
├── config/                # Configuration files
│   └── database.php      # Database configuration
├── database/              # Database files
│   ├── schema.sql        # Database schema
│   └── sample_data.sql   # Sample data
├── includes/              # Common includes
│   ├── auth.php          # Authentication functions
│   └── functions.php     # Utility functions
├── index.php             # Homepage
├── shop.php              # Product listing
├── login.php             # User login
├── register.php          # User registration
└── README.md             # This file
```

## Security Considerations

### Password Security
- All passwords are hashed using PHP's `password_hash()`
- Minimum password requirements enforced
- Password reset functionality with secure tokens

### Session Security
- Session regeneration on login
- Secure session configuration
- Session timeout handling

### Input Validation
- All user inputs are sanitized
- SQL injection prevention with prepared statements
- XSS protection with output encoding

### CSRF Protection
- CSRF tokens on all forms
- Token validation on form submissions

## Customization

### Adding New Features

1. **Database Changes:**
   - Add new tables to `database/schema.sql`
   - Update sample data if needed

2. **Admin Panel:**
   - Create new PHP files in `/admin/`
   - Add navigation links in `admin/includes/header.php`
   - Follow existing patterns for consistency

3. **Frontend:**
   - Modify existing templates
   - Add new pages following the template structure
   - Update navigation in headers

### Styling Customization

1. **Colors:**
   - Primary color: `#59ab6e` (green)
   - Update in `assets/css/templatemo.css`

2. **Layout:**
   - Bootstrap 5 framework
   - Responsive design patterns
   - Custom CSS in `assets/css/custom.css`

## API Endpoints

The system includes AJAX endpoints for dynamic functionality:

- `/api/products.php` - Product operations
- `/api/cart.php` - Shopping cart operations
- `/api/orders.php` - Order operations
- `/api/users.php` - User operations

## Troubleshooting

### Common Issues

1. **Database Connection Error:**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **Permission Errors:**
   - Check file permissions on upload directories
   - Ensure web server has write access

3. **Session Issues:**
   - Check PHP session configuration
   - Verify session directory permissions

4. **Image Upload Problems:**
   - Check GD extension is installed
   - Verify upload directory permissions
   - Check file size limits in PHP configuration

### Debug Mode

Enable debug mode by adding to `config/database.php`:
```php
define('DEBUG_MODE', true);
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Support

For support and questions:
- Check the documentation
- Review error logs
- Ensure all requirements are met

## License

This project is built on the TemplateMo 559 Zay Shop template and follows the same licensing terms. The template is free for commercial use.

## Credits

- **Template:** TemplateMo 559 Zay Shop
- **Framework:** Bootstrap 5
- **Icons:** Font Awesome
- **Fonts:** Google Fonts (Roboto)

---

**Note:** This is a comprehensive e-commerce management system. Always backup your data before making changes and test thoroughly in a development environment before deploying to production.
