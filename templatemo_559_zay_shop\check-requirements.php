<?php
/**
 * فحص متطلبات النظام لمتجر زاي الإلكتروني
 * تشغيل هذا الملف للتأكد من توافق الخادم
 */

$requirements = [
    'PHP Version' => [
        'required' => '7.4.0',
        'current' => PHP_VERSION,
        'status' => version_compare(PHP_VERSION, '7.4.0', '>=')
    ],
    'MySQL Extension' => [
        'required' => 'Required',
        'current' => extension_loaded('mysql') || extension_loaded('mysqli') || extension_loaded('pdo_mysql') ? 'Available' : 'Not Available',
        'status' => extension_loaded('mysql') || extension_loaded('mysqli') || extension_loaded('pdo_mysql')
    ],
    'PDO Extension' => [
        'required' => 'Required',
        'current' => extension_loaded('pdo') ? 'Available' : 'Not Available',
        'status' => extension_loaded('pdo')
    ],
    'GD Extension' => [
        'required' => 'Required',
        'current' => extension_loaded('gd') ? 'Available' : 'Not Available',
        'status' => extension_loaded('gd')
    ],
    'cURL Extension' => [
        'required' => 'Required',
        'current' => extension_loaded('curl') ? 'Available' : 'Not Available',
        'status' => extension_loaded('curl')
    ],
    'mbstring Extension' => [
        'required' => 'Required',
        'current' => extension_loaded('mbstring') ? 'Available' : 'Not Available',
        'status' => extension_loaded('mbstring')
    ],
    'File Uploads' => [
        'required' => 'Enabled',
        'current' => ini_get('file_uploads') ? 'Enabled' : 'Disabled',
        'status' => ini_get('file_uploads')
    ],
    'Session Support' => [
        'required' => 'Enabled',
        'current' => extension_loaded('session') ? 'Enabled' : 'Disabled',
        'status' => extension_loaded('session')
    ]
];

$directories = [
    'assets/img' => 'assets/img',
    'config' => 'config',
    'database' => 'database'
];

$allPassed = true;
foreach ($requirements as $req) {
    if (!$req['status']) {
        $allPassed = false;
        break;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <title>فحص متطلبات النظام - متجر زاي</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fc; }
        .container { max-width: 800px; margin: 50px auto; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .card { border: none; box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15); }
    </style>
</head>

<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white text-center">
                <h2 class="mb-0">
                    <i class="fas fa-server me-2"></i>
                    فحص متطلبات النظام
                </h2>
                <p class="mb-0 mt-2">متجر زاي الإلكتروني</p>
            </div>
            
            <div class="card-body">
                <?php if ($allPassed): ?>
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h4>ممتاز! خادمك يدعم جميع المتطلبات</h4>
                        <p>يمكنك المتابعة لتثبيت المتجر الإلكتروني</p>
                        <a href="install.php" class="btn btn-success btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            ابدأ التثبيت الآن
                        </a>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h4>يرجى حل المشاكل التالية قبل التثبيت</h4>
                    </div>
                <?php endif; ?>

                <h5 class="mb-3">
                    <i class="fas fa-cogs me-2"></i>
                    متطلبات PHP والإضافات
                </h5>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المتطلب</th>
                                <th>مطلوب</th>
                                <th>الحالي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($requirements as $name => $req): ?>
                                <tr>
                                    <td><?php echo $name; ?></td>
                                    <td><?php echo $req['required']; ?></td>
                                    <td><?php echo $req['current']; ?></td>
                                    <td>
                                        <?php if ($req['status']): ?>
                                            <i class="fas fa-check-circle status-pass"></i>
                                            <span class="status-pass">متوفر</span>
                                        <?php else: ?>
                                            <i class="fas fa-times-circle status-fail"></i>
                                            <span class="status-fail">غير متوفر</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <h5 class="mb-3 mt-4">
                    <i class="fas fa-folder me-2"></i>
                    فحص المجلدات والصلاحيات
                </h5>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المجلد</th>
                                <th>موجود</th>
                                <th>قابل للكتابة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($directories as $name => $path): ?>
                                <?php 
                                $exists = is_dir($path);
                                $writable = $exists && is_writable($path);
                                $status = $exists && $writable;
                                ?>
                                <tr>
                                    <td><?php echo $name; ?></td>
                                    <td>
                                        <?php if ($exists): ?>
                                            <i class="fas fa-check status-pass"></i>
                                        <?php else: ?>
                                            <i class="fas fa-times status-fail"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($writable): ?>
                                            <i class="fas fa-check status-pass"></i>
                                        <?php else: ?>
                                            <i class="fas fa-times status-fail"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($status): ?>
                                            <span class="status-pass">جاهز</span>
                                        <?php else: ?>
                                            <span class="status-fail">يحتاج إصلاح</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الخادم</h6>
                        <ul class="list-unstyled">
                            <li><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></li>
                            <li><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></li>
                            <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                            <li><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-upload me-2"></i>إعدادات الرفع</h6>
                        <ul class="list-unstyled">
                            <li><strong>حد رفع الملفات:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
                            <li><strong>حد POST:</strong> <?php echo ini_get('post_max_size'); ?></li>
                            <li><strong>وقت التنفيذ الأقصى:</strong> <?php echo ini_get('max_execution_time'); ?>s</li>
                            <li><strong>وقت الإدخال الأقصى:</strong> <?php echo ini_get('max_input_time'); ?>s</li>
                        </ul>
                    </div>
                </div>

                <?php if (!$allPassed): ?>
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-lightbulb me-2"></i>نصائح لحل المشاكل:</h6>
                        <ul class="mb-0">
                            <li>تواصل مع مزود الاستضافة لتفعيل الإضافات المطلوبة</li>
                            <li>تأكد من أن إصدار PHP 7.4 أو أحدث</li>
                            <li>تحقق من صلاحيات المجلدات (755 للمجلدات، 644 للملفات)</li>
                            <li>فعل إضافات GD و cURL و mbstring</li>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="text-center mt-4">
                    <a href="index.html" class="btn btn-secondary me-2">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                    <?php if ($allPassed): ?>
                        <a href="install.php" class="btn btn-success">
                            <i class="fas fa-play me-2"></i>
                            بدء التثبيت
                        </a>
                    <?php else: ?>
                        <button class="btn btn-success" disabled>
                            <i class="fas fa-times me-2"></i>
                            حل المشاكل أولاً
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث الصفحة كل 30 ثانية للتحقق من التحديثات
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
