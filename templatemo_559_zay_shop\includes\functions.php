<?php
/**
 * Common Functions for Zay Shop E-commerce System
 * 
 * This file contains utility functions used throughout the application
 * for various operations like formatting, validation, and data processing.
 */

require_once __DIR__ . '/../config/database.php';

/**
 * Format price with currency symbol
 * @param float $price
 * @param string $currency
 * @return string
 */
function formatPrice($price, $currency = 'USD') {
    $symbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'JPY' => '¥'
    ];
    
    $symbol = $symbols[$currency] ?? '$';
    return $symbol . number_format($price, 2);
}

/**
 * Calculate discount percentage
 * @param float $originalPrice
 * @param float $salePrice
 * @return int
 */
function calculateDiscountPercentage($originalPrice, $salePrice) {
    if ($originalPrice <= 0) return 0;
    return round((($originalPrice - $salePrice) / $originalPrice) * 100);
}

/**
 * Generate product URL slug
 * @param string $name
 * @return string
 */
function generateSlug($name) {
    $slug = strtolower(trim($name));
    $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    return trim($slug, '-');
}

/**
 * Generate unique SKU
 * @param string $prefix
 * @return string
 */
function generateSKU($prefix = 'PROD') {
    return strtoupper($prefix) . '-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * Upload and process image
 * @param array $file
 * @param string $uploadDir
 * @param array $allowedTypes
 * @param int $maxSize
 * @return array
 */
function uploadImage($file, $uploadDir = 'assets/img/products/', $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'], $maxSize = 5242880) {
    try {
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'File upload failed'];
        }
        
        // Check file size
        if ($file['size'] > $maxSize) {
            return ['success' => false, 'message' => 'File size too large. Maximum size is ' . ($maxSize / 1024 / 1024) . 'MB'];
        }
        
        // Get file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        // Check file type
        if (!in_array($extension, $allowedTypes)) {
            return ['success' => false, 'message' => 'Invalid file type. Allowed types: ' . implode(', ', $allowedTypes)];
        }
        
        // Generate unique filename
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
        } else {
            return ['success' => false, 'message' => 'Failed to move uploaded file'];
        }
        
    } catch (Exception $e) {
        error_log("Image upload failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Upload failed. Please try again.'];
    }
}

/**
 * Resize image
 * @param string $source
 * @param string $destination
 * @param int $width
 * @param int $height
 * @param int $quality
 * @return bool
 */
function resizeImage($source, $destination, $width, $height, $quality = 90) {
    try {
        $imageInfo = getimagesize($source);
        if (!$imageInfo) return false;
        
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $sourceType = $imageInfo[2];
        
        // Calculate aspect ratio
        $aspectRatio = $sourceWidth / $sourceHeight;
        
        if ($width / $height > $aspectRatio) {
            $width = $height * $aspectRatio;
        } else {
            $height = $width / $aspectRatio;
        }
        
        // Create source image
        switch ($sourceType) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($source);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($source);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($source);
                break;
            default:
                return false;
        }
        
        // Create destination image
        $destImage = imagecreatetruecolor($width, $height);
        
        // Preserve transparency for PNG and GIF
        if ($sourceType == IMAGETYPE_PNG || $sourceType == IMAGETYPE_GIF) {
            imagealphablending($destImage, false);
            imagesavealpha($destImage, true);
            $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
            imagefilledrectangle($destImage, 0, 0, $width, $height, $transparent);
        }
        
        // Resize image
        imagecopyresampled($destImage, $sourceImage, 0, 0, 0, 0, $width, $height, $sourceWidth, $sourceHeight);
        
        // Save image
        $result = false;
        switch ($sourceType) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($destImage, $destination, $quality);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($destImage, $destination);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($destImage, $destination);
                break;
        }
        
        // Clean up memory
        imagedestroy($sourceImage);
        imagedestroy($destImage);
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Image resize failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Paginate results
 * @param int $totalItems
 * @param int $itemsPerPage
 * @param int $currentPage
 * @return array
 */
function paginate($totalItems, $itemsPerPage = 12, $currentPage = 1) {
    $totalPages = ceil($totalItems / $itemsPerPage);
    $currentPage = max(1, min($totalPages, $currentPage));
    $offset = ($currentPage - 1) * $itemsPerPage;
    
    return [
        'total_items' => $totalItems,
        'items_per_page' => $itemsPerPage,
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'previous_page' => $currentPage > 1 ? $currentPage - 1 : null,
        'next_page' => $currentPage < $totalPages ? $currentPage + 1 : null
    ];
}

/**
 * Generate pagination HTML
 * @param array $pagination
 * @param string $baseUrl
 * @return string
 */
function generatePaginationHTML($pagination, $baseUrl) {
    if ($pagination['total_pages'] <= 1) {
        return '';
    }
    
    $html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';
    
    // Previous button
    if ($pagination['has_previous']) {
        $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $pagination['previous_page'] . '">Previous</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><span class="page-link">Previous</span></li>';
    }
    
    // Page numbers
    $start = max(1, $pagination['current_page'] - 2);
    $end = min($pagination['total_pages'], $pagination['current_page'] + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $pagination['current_page']) {
            $html .= '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
        } else {
            $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $i . '">' . $i . '</a></li>';
        }
    }
    
    // Next button
    if ($pagination['has_next']) {
        $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $pagination['next_page'] . '">Next</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><span class="page-link">Next</span></li>';
    }
    
    $html .= '</ul></nav>';
    
    return $html;
}

/**
 * Get website setting
 * @param string $key
 * @param mixed $default
 * @return mixed
 */
function getSetting($key, $default = null) {
    try {
        $sql = "SELECT setting_value, setting_type FROM website_settings WHERE setting_key = ?";
        $setting = dbFetchOne($sql, [$key]);
        
        if (!$setting) {
            return $default;
        }
        
        $value = $setting['setting_value'];
        
        // Convert based on type
        switch ($setting['setting_type']) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($value) ? (float)$value : $default;
            case 'json':
                return json_decode($value, true) ?: $default;
            default:
                return $value;
        }
        
    } catch (Exception $e) {
        error_log("Get setting failed: " . $e->getMessage());
        return $default;
    }
}

/**
 * Update website setting
 * @param string $key
 * @param mixed $value
 * @param string $type
 * @return bool
 */
function updateSetting($key, $value, $type = 'text') {
    try {
        // Convert value based on type
        switch ($type) {
            case 'boolean':
                $value = $value ? 'true' : 'false';
                break;
            case 'json':
                $value = json_encode($value);
                break;
            default:
                $value = (string)$value;
        }
        
        $sql = "INSERT INTO website_settings (setting_key, setting_value, setting_type) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = ?, setting_type = ?, updated_at = NOW()";
        
        dbExecute($sql, [$key, $value, $type, $value, $type]);
        return true;
        
    } catch (Exception $e) {
        error_log("Update setting failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Send email notification
 * @param string $to
 * @param string $subject
 * @param string $message
 * @param array $headers
 * @return bool
 */
function sendEmail($to, $subject, $message, $headers = []) {
    try {
        $defaultHeaders = [
            'From' => getSetting('contact_email', '<EMAIL>'),
            'Reply-To' => getSetting('contact_email', '<EMAIL>'),
            'Content-Type' => 'text/html; charset=UTF-8',
            'X-Mailer' => 'Zay Shop'
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        $headerString = '';
        
        foreach ($headers as $key => $value) {
            $headerString .= $key . ': ' . $value . "\r\n";
        }
        
        return mail($to, $subject, $message, $headerString);
        
    } catch (Exception $e) {
        error_log("Send email failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate order number
 * @return string
 */
function generateOrderNumber() {
    return 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Calculate tax amount
 * @param float $amount
 * @param float $taxRate
 * @return float
 */
function calculateTax($amount, $taxRate = null) {
    if ($taxRate === null) {
        $taxRate = getSetting('tax_rate', 0);
    }
    return round($amount * ($taxRate / 100), 2);
}

/**
 * Calculate shipping cost
 * @param float $orderTotal
 * @param float $weight
 * @return float
 */
function calculateShipping($orderTotal, $weight = 0) {
    $freeShippingThreshold = getSetting('free_shipping_threshold', 100);
    
    if ($orderTotal >= $freeShippingThreshold) {
        return 0;
    }
    
    return getSetting('shipping_cost', 10);
}

/**
 * Format date for display
 * @param string $date
 * @param string $format
 * @return string
 */
function formatDate($date, $format = 'M j, Y') {
    return date($format, strtotime($date));
}

/**
 * Time ago function
 * @param string $datetime
 * @return string
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}
?>
