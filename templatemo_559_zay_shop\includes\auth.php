<?php
/**
 * Authentication and Security Functions for Zay Shop
 * 
 * This file contains all authentication, session management,
 * and security-related functions for the e-commerce system.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../config/database.php';

/**
 * Generate CSRF token
 * @return string
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * @param string $token
 * @return bool
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Sanitize input data
 * @param mixed $data
 * @return mixed
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 * @param string $email
 * @return bool
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Hash password securely
 * @param string $password
 * @return string
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password against hash
 * @param string $password
 * @param string $hash
 * @return bool
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate secure random string
 * @param int $length
 * @return string
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Register new user
 * @param array $userData
 * @return array
 */
function registerUser($userData) {
    try {
        // Validate required fields
        $required = ['username', 'email', 'password', 'first_name', 'last_name'];
        foreach ($required as $field) {
            if (empty($userData[$field])) {
                return ['success' => false, 'message' => "Field {$field} is required"];
            }
        }
        
        // Validate email
        if (!validateEmail($userData['email'])) {
            return ['success' => false, 'message' => 'Invalid email address'];
        }
        
        // Check if username or email already exists
        $checkSql = "SELECT id FROM users WHERE username = ? OR email = ?";
        $existing = dbFetchOne($checkSql, [$userData['username'], $userData['email']]);
        
        if ($existing) {
            return ['success' => false, 'message' => 'Username or email already exists'];
        }
        
        // Hash password
        $hashedPassword = hashPassword($userData['password']);
        
        // Insert user
        $insertSql = "INSERT INTO users (username, email, password_hash, first_name, last_name, phone, address, city, state, zip_code, role) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $userData['username'],
            $userData['email'],
            $hashedPassword,
            $userData['first_name'],
            $userData['last_name'],
            $userData['phone'] ?? null,
            $userData['address'] ?? null,
            $userData['city'] ?? null,
            $userData['state'] ?? null,
            $userData['zip_code'] ?? null,
            $userData['role'] ?? 'customer'
        ];
        
        dbExecute($insertSql, $params);
        $userId = dbLastInsertId();
        
        return ['success' => true, 'message' => 'User registered successfully', 'user_id' => $userId];
        
    } catch (Exception $e) {
        error_log("User registration failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Registration failed. Please try again.'];
    }
}

/**
 * Authenticate user login
 * @param string $username
 * @param string $password
 * @return array
 */
function authenticateUser($username, $password) {
    try {
        // Get user by username or email
        $sql = "SELECT * FROM users WHERE (username = ? OR email = ?) AND status = 'active'";
        $user = dbFetchOne($sql, [$username, $username]);
        
        if (!$user) {
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
        
        // Verify password
        if (!verifyPassword($password, $user['password_hash'])) {
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
        
        // Update last login
        $updateSql = "UPDATE users SET last_login = NOW() WHERE id = ?";
        dbExecute($updateSql, [$user['id']]);
        
        // Set session data
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['logged_in'] = true;
        
        // Regenerate session ID for security
        session_regenerate_id(true);
        
        return ['success' => true, 'message' => 'Login successful', 'user' => $user];
        
    } catch (Exception $e) {
        error_log("User authentication failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Authentication failed. Please try again.'];
    }
}

/**
 * Check if user is logged in
 * @return bool
 */
function isLoggedIn() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

/**
 * Check if user is admin
 * @return bool
 */
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * Get current user data
 * @return array|null
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $sql = "SELECT * FROM users WHERE id = ? AND status = 'active'";
        return dbFetchOne($sql, [$_SESSION['user_id']]);
    } catch (Exception $e) {
        error_log("Get current user failed: " . $e->getMessage());
        return null;
    }
}

/**
 * Logout user
 */
function logoutUser() {
    // Clear session data
    $_SESSION = [];
    
    // Destroy session cookie
    if (isset($_COOKIE[session_name()])) {
        setcookie(session_name(), '', time() - 3600, '/');
    }
    
    // Destroy session
    session_destroy();
}

/**
 * Require login (redirect if not logged in)
 * @param string $redirectUrl
 */
function requireLogin($redirectUrl = '/login.php') {
    if (!isLoggedIn()) {
        header("Location: {$redirectUrl}");
        exit;
    }
}

/**
 * Require admin access
 * @param string $redirectUrl
 */
function requireAdmin($redirectUrl = '/index.php') {
    if (!isAdmin()) {
        header("Location: {$redirectUrl}");
        exit;
    }
}

/**
 * Log admin action
 * @param string $action
 * @param string $tableName
 * @param int $recordId
 * @param array $oldValues
 * @param array $newValues
 */
function logAdminAction($action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null) {
    if (!isAdmin()) {
        return;
    }
    
    try {
        $sql = "INSERT INTO admin_logs (admin_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $_SESSION['user_id'],
            $action,
            $tableName,
            $recordId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        dbExecute($sql, $params);
    } catch (Exception $e) {
        error_log("Admin action logging failed: " . $e->getMessage());
    }
}

/**
 * Rate limiting for login attempts
 * @param string $identifier
 * @param int $maxAttempts
 * @param int $timeWindow
 * @return bool
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 900) {
    $key = "login_attempts_" . md5($identifier);
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
    }
    
    $attempts = $_SESSION[$key];
    
    // Reset if time window has passed
    if (time() - $attempts['first_attempt'] > $timeWindow) {
        $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
        return true;
    }
    
    // Check if limit exceeded
    if ($attempts['count'] >= $maxAttempts) {
        return false;
    }
    
    return true;
}

/**
 * Record login attempt
 * @param string $identifier
 */
function recordLoginAttempt($identifier) {
    $key = "login_attempts_" . md5($identifier);
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
    }
    
    $_SESSION[$key]['count']++;
}

/**
 * Clear login attempts
 * @param string $identifier
 */
function clearLoginAttempts($identifier) {
    $key = "login_attempts_" . md5($identifier);
    unset($_SESSION[$key]);
}
?>
