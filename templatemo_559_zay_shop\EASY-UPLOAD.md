# 📤 الطريقة الأسهل للرفع - خطوة بخطوة

## 🎯 اتبع هذه الخطوات بالضبط:

### الخطوة 1: تحضير الملفات
```
1. اضغط بالزر الأيمن على مجلد templatemo_559_zay_shop
2. اختر "Send to" > "Compressed folder" (في Windows)
   أو "Compress" (في Mac)
3. سيتم إنشاء ملف مضغوط
4. اسم الملف: templatemo_559_zay_shop.zip
```

### الخطوة 2: الدخول لـ File Manager
```
1. من لوحة تحكم InfinityFree
2. اضغط "Control Panel" بجانب اسم موقعك
3. ابحث عن "File Manager" واضغط عليه
4. ستفتح نافذة جديدة
5. اضغط على مجلد "htdocs"
```

### الخطوة 3: رفع الملف المضغوط
```
1. ا<PERSON><PERSON><PERSON> "Upload" من الشريط العلوي
2. اضغ<PERSON> "Select File"
3. اختر ملف templatemo_559_zay_shop.zip
4. انتظر انتهاء الرفع (شريط أخضر)
5. اضغط "Go Back to htdocs"
```

### الخطوة 4: فك الضغط
```
1. ستجد الملف المضغوط في القائمة
2. اضغط بالزر الأيمن عليه
3. اختر "Extract"
4. اضغط "Extract File(s)"
5. انتظر انتهاء فك الضغط
```

### الخطوة 5: نقل الملفات
```
1. ستجد مجلد templatemo_559_zay_shop
2. اضغط عليه مرتين لفتحه
3. اضغط "Select All" لتحديد جميع الملفات
4. اضغط "Move" من الشريط العلوي
5. اختر مجلد htdocs
6. اضغط "Move File(s)"
```

### الخطوة 6: تنظيف
```
1. ارجع لمجلد htdocs
2. احذف الملف المضغوط
3. احذف المجلد الفارغ templatemo_559_zay_shop
```

## 🗄️ إعداد قاعدة البيانات:

### في cPanel:
```
1. اضغط "MySQL Databases"
2. في "Create New Database":
   - اكتب: zay_shop
   - اضغط "Create Database"
3. في "MySQL Users":
   - Username: zay_user
   - Password: [كلمة مرور قوية]
   - اضغط "Create User"
4. في "Add User to Database":
   - اختر zay_user و zay_shop
   - اضغط "Add"
   - اختر "ALL PRIVILEGES"
   - اضغط "Make Changes"
```

## ⚙️ تشغيل التثبيت:

### انتقل إلى موقعك:
```
https://yoursite.epizy.com/install.php

اتبع الخطوات:
1. اضغط "Start Installation"
2. أدخل بيانات قاعدة البيانات:
   - Host: localhost
   - Database: [username]_zay_shop
   - User: [username]_zay_user
   - Password: [كلمة مرورك]
3. أنشئ حساب المدير
4. اضغط "Complete Installation"
```

## 🎉 انتهيت!

موقعك الآن جاهز على:
https://yoursite.epizy.com

لوحة الإدارة:
https://yoursite.epizy.com/admin/login.php

---

## 🆘 إذا واجهت مشكلة:

### مشكلة في الرفع:
- تأكد من حجم الملف أقل من 50MB
- جرب رفع الملفات على دفعات
- استخدم متصفح مختلف

### مشكلة في قاعدة البيانات:
- تأكد من كتابة الاسم الكامل للقاعدة
- تحقق من كلمة المرور
- جرب إنشاء قاعدة بيانات جديدة

### مشكلة في التثبيت:
- تأكد من وجود جميع الملفات
- تحقق من صلاحيات المجلدات
- جرب حذف ملفات التثبيت وإعادة الرفع

---

## 💡 نصائح مهمة:

1. **احفظ بيانات قاعدة البيانات** في مكان آمن
2. **غير كلمة مرور المدير** بعد التثبيت
3. **احذف install.php** بعد انتهاء التثبيت
4. **اعمل نسخة احتياطية** من الملفات

**الوقت المطلوب: 10-15 دقيقة فقط!**
